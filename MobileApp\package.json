{"name": "MobileApp", "version": "1.0.0", "scripts": {"android": "expo start --android", "ios": "expo start --ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@types/react-native-vector-icons": "^6.4.18", "axios": "^1.10.0", "expo": "^53.0.15", "expo-dev-client": "~5.2.4", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.16", "expo-video": "~2.2.2", "nativewind": "latest", "react": "19.0.0", "react-native": "0.79.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-image-picker": "^8.2.1", "react-native-image-viewing": "^0.2.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-toast-message": "^2.3.3", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "eslint-config-expo": "^9.2.0", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.0", "typescript": "~5.8.3"}, "main": "node_modules/expo/AppEntry.js", "private": true}